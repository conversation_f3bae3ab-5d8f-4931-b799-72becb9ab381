# Flow Highlighting Feature

## Overview
When a node is clicked or selected in the Shoshin editor, the system automatically highlights all nodes and edges in the linear flow from the start node to the selected node.

## How It Works

### Algorithm
1. **Find Start Nodes**: Identifies all nodes with `data.type === "start"`
2. **Build Graph**: Creates an adjacency list from the edges
3. **BFS Search**: Uses Breadth-First Search to find the shortest path from any start node to the selected node
4. **Highlight Path**: Highlights all nodes and edges in the discovered path

### Visual Indicators
- **Selected Node**: Blue border with shadow (existing behavior)
- **Highlighted Nodes**: Amber border with glow effect
- **Highlighted Edges**: Amber color with glow effect and thicker stroke
- **Normal Elements**: Default grey styling

## Testing Instructions

### 1. Open the Editor
Navigate to: `http://localhost:3001/editor`

### 2. Test Flow Highlighting
Click on different nodes to see the flow highlighting:

- **Click "Response" node**: Should highlight `Start → LLM → Knowledge → Response`
- **Click "Knowledge" node**: Should highlight `Start → LLM → Knowledge`
- **Click "Function" node**: Should highlight `Start → Condition → Function`
- **Click "Start" node**: Should highlight only the `Start` node

### 3. Test Edge Cases
- **Multiple Selection**: Hold Ctrl/Cmd and select multiple nodes → highlighting should clear
- **Deselection**: Click empty canvas area → all highlighting should clear
- **No Path**: If a node has no path from start, no highlighting occurs

### 4. Debug Console Commands
Open browser console and use these debug commands:

```javascript
// Test flow highlighting for a specific node
testFlowHighlighting('response-1')

// Check current store state
useEditorStore.getState().highlightedNodes
useEditorStore.getState().highlightedEdges

// Manually trigger highlighting
useEditorStore.getState().highlightFlow('knowledge-1')

// Clear highlighting
useEditorStore.getState().clearHighlight()
```

## Implementation Details

### Store State
```typescript
interface EditorState {
  // ... existing state
  highlightedNodes: string[];  // Array of highlighted node IDs
  highlightedEdges: string[];  // Array of highlighted edge IDs
  
  // New actions
  highlightFlow: (nodeId: string) => void;
  clearHighlight: () => void;
}
```

### Key Functions
- `buildLinearFlow()`: Main algorithm that finds the path
- `findPath()`: BFS implementation for pathfinding
- `updateSelection()`: Triggers highlighting on selection changes

### Component Updates
- **CustomNode**: Checks `highlightedNodes` and applies amber styling
- **ShoshinEdge**: Checks `highlightedEdges` and applies amber styling

## Expected Behavior

1. **Single Node Selection**: Highlights the linear flow from start to selected node
2. **Multiple Node Selection**: Clears all highlighting
3. **No Selection**: Clears all highlighting
4. **Start Node Selection**: Highlights only the start node itself
5. **Unreachable Nodes**: No highlighting if no path exists from start

## Troubleshooting

If highlighting doesn't work:
1. Check browser console for errors
2. Verify `window.useEditorStore` is available
3. Test with debug commands
4. Check that nodes have correct `data.type` values
5. Verify edges are properly connected

## Performance Notes
- BFS algorithm is efficient for typical workflow sizes
- Highlighting state is managed in Zustand store
- Visual updates use CSS transitions for smooth effects
