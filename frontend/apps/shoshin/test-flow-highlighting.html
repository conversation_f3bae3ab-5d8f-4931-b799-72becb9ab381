<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flow Highlighting Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-step {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .test-step h3 {
            margin-top: 0;
            color: #333;
        }
        .expected {
            color: #0066cc;
            font-weight: bold;
        }
        .note {
            color: #666;
            font-style: italic;
            margin-top: 10px;
        }
        .highlight-demo {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 4px;
        }
        .highlighted-node {
            background-color: #fbbf24;
            color: white;
            border: 2px solid #f59e0b;
        }
        .selected-node {
            background-color: #3b82f6;
            color: white;
            border: 2px solid #2563eb;
        }
        .normal-node {
            background-color: #e5e7eb;
            color: #374151;
            border: 2px solid #d1d5db;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Flow Highlighting Test Instructions</h1>
        <p>This test verifies that the flow highlighting feature works correctly when nodes are selected in the Shoshin editor.</p>
        
        <div class="test-step">
            <h3>Step 1: Open the Editor</h3>
            <p>Navigate to <a href="http://localhost:3001/editor" target="_blank">http://localhost:3001/editor</a></p>
            <p class="expected">Expected: You should see the ReactFlow editor with several pre-loaded nodes and edges.</p>
        </div>

        <div class="test-step">
            <h3>Step 2: Test Flow Highlighting</h3>
            <p>Click on different nodes to test the flow highlighting functionality:</p>
            <ul>
                <li><strong>Click on the "Response" node</strong> - This should highlight the linear flow from the "Start" node to the "Response" node</li>
                <li><strong>Click on the "Knowledge" node</strong> - This should highlight the flow from "Start" → "LLM" → "Knowledge"</li>
                <li><strong>Click on the "Function" node</strong> - This should highlight the flow from "Start" → "Condition" → "Function"</li>
            </ul>
            <p class="expected">Expected Visual Behavior:</p>
            <ul>
                <li>Selected node: <span class="highlight-demo selected-node">Blue border with shadow</span></li>
                <li>Highlighted nodes in flow: <span class="highlight-demo highlighted-node">Amber border with glow</span></li>
                <li>Highlighted edges: <span style="color: #fbbf24; font-weight: bold;">Amber color with glow effect</span></li>
                <li>Normal nodes/edges: <span class="highlight-demo normal-node">Default grey styling</span></li>
            </ul>
        </div>

        <div class="test-step">
            <h3>Step 3: Test Multiple Selection</h3>
            <p>Hold Ctrl/Cmd and click multiple nodes to select them.</p>
            <p class="expected">Expected: When multiple nodes are selected, highlighting should be cleared (no amber highlighting).</p>
        </div>

        <div class="test-step">
            <h3>Step 4: Test Deselection</h3>
            <p>Click on an empty area of the canvas to deselect all nodes.</p>
            <p class="expected">Expected: All highlighting should be cleared, returning to normal grey styling.</p>
        </div>

        <div class="test-step">
            <h3>Step 5: Test Start Node Selection</h3>
            <p>Click on the "Start" node itself.</p>
            <p class="expected">Expected: Only the "Start" node should be highlighted (since it's both the start and target of the flow).</p>
        </div>

        <div class="test-step">
            <h3>Troubleshooting</h3>
            <p>If the highlighting doesn't work:</p>
            <ul>
                <li>Open browser developer tools (F12) and check the console for errors</li>
                <li>Verify that the Zustand store is working by checking <code>window.useEditorStore</code> in the console</li>
                <li>Check that the <code>highlightedNodes</code> and <code>highlightedEdges</code> arrays are being populated when nodes are selected</li>
            </ul>
            <p class="note">Note: The flow highlighting uses BFS (Breadth-First Search) to find the shortest path from any start node to the selected node.</p>
        </div>
    </div>
</body>
</html>
